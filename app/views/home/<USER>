<!-- 引入样式文件 -->
<link
  rel="stylesheet"
  href="/vant.css"
/>

<!-- 引入 Vue 和 Vant 的 JS 文件 -->
<script src="/vue.global.min.js"></script>
<script src="/vant.min.js"></script>
<script src="/axios.min.js"></script>

<style>
  .van-picker-column .van-ellipsis {
    overflow: auto !important;
    white-space: initial !important;
  }

  .van-picker-column:first-child .van-ellipsis {
    overflow: hidden !important;
    white-space: nowrap !important;
    font-size: 20px;
  }

  .van-stepper__input {
    font-size: 18px;
  }

  .van-tabs__line {
    background: #FF7F65;
  }
</style>
<div class="container">
  <div id="app" class="mt-3">
    <van-pull-refresh v-model="pull_loading" @refresh="onRefresh">
      <van-tabs v-model:active="active" @click-tab="onClickTab" animated>
        <van-tab title="国内"></van-tab>
        <van-tab title="美亚"></van-tab>
        <van-tab title="英亚"></van-tab>
        <van-tab title="欧亚"></van-tab>
        <van-tab title="日亚"></van-tab>
        <!-- <van-tab title="渠道"></van-tab> -->
      </van-tabs>
      <van-search v-model="search_value" @update:model-value="onSearch" placeholder="请输入搜索关键词" shape="round"></van-search>
      <van-picker title="请选择商品" :columns="columns" v-model="selectedValues" :swipe-duration="500" confirm-button-text=" " cancel-button-text=" " visible-option-num="11" @change="onChange"></van-picker>
      <div class="text-center mb-5 mt-4">
        <van-stepper v-model="value" input-width="40px" button-size="40px"></van-stepper>
      </div>
      <van-button v-if="(country_code == '美亚' || country_code == '欧亚' || country_code == '英亚' || country_code == '日亚') || !isEZ80Product || deviceConnected" type="success" class="mb-4" size="large" @click="print" :loading="is_loading" color="#FF7F65" loading-type="spinner" loading-text="正在打印中...">打印标签</van-button>
      <van-button v-if="(country_code == '渠道' || country_code == '国内') && isEZ80Product && !deviceConnected" type="success" class="mb-4" size="large" @click="connectDevice" color="#FF7F65" loading-type="spinner" loading-text="正在连接设备...">获取SN码</van-button>
      <a href="/home/<USER>" target="_blank">根据SN码打印</a>
    </van-pull-refresh>
  </div>
</div>
<script>
  const App = {
    setup() {
      const value = Vue.ref('');
      const active = Vue.ref(0);
      const country_code = Vue.ref('国内');
      const is_loading = Vue.ref(false);
      const pull_loading = Vue.ref(false);
      const selectedValues = Vue.ref(['6']);
      const search_value = Vue.ref('');
      const columns = Vue.ref(<%= raw @products %>);
      const deviceConnected = Vue.ref(false);
      // 修正 isSpecialProduct 为异步函数，返回 Promise
      const isSpecialProduct = async (product_id) => {
        try {
          const response = await axios.get('/home/<USER>', {
            params: {
              product_id: product_id
            }
          });
          console.log(response);
          return response.data.need_sn;
        } catch (error) {
          console.log(error);
          return false;
        }
      };

      // 初始化时异步判断是否为EZ80产品
      const isEZ80Product = Vue.ref(false);
      const skuName = Vue.ref(columns.value[0].children[0].text);
      const productName = Vue.ref(columns.value[0].text);
      const sn_code = Vue.ref('');

      // 页面加载时初始化 isEZ80Product
      isSpecialProduct(columns.value[0].value).then((needSn) => {
        isEZ80Product.value = needSn;
      });

      const onChange = async (picker, value, index) => {
        deviceConnected.value = false;
        console.log(picker.selectedOptions[1].text);
        const needSn = await isSpecialProduct(picker.selectedOptions[0].value);
        if (needSn) {
          skuName.value = picker.selectedOptions[1].text;
          productName.value = picker.selectedOptions[0].text;
          isEZ80Product.value = true;
        } else {
          isEZ80Product.value = false;
        }
      }

      const onSearch = (val) => {
        axios
          .get('/home/<USER>' + val, {
            params: {
              country_code: country_code.value
            }
          })
          .then(async (response) => {
            columns.value = response.data;
            const needSn = await isSpecialProduct(response.data[0].value);
            isEZ80Product.value = needSn;
            if (needSn) {
              skuName.value = response.data[0].children[0].text;
              productName.value = response.data[0].text;
              isEZ80Product.value = true;
            } else {
              isEZ80Product.value = false;
            }
            console.log(response);
          });
      }

      const print = () => {
        is_loading.value = true;
        var sku_id = selectedValues.value[1]
        var print_times = value.value
        console.log(sn_code, 'sn_code')
        axios.post('/home/<USER>', {
          sku_id: sku_id,
          print_times: print_times,
          country_code: country_code.value,
          device_code: '<%= params[:device_code] %>',
          sn_code: sn_code.value
        })
        .then((response) => {
          is_loading.value = false;
          if(response.data.success == true){
            vant.showNotify({ type: 'success', message: response.data.message });
          }else {
            vant.showNotify({ type: 'danger', message: response.data.message });
          }
        })
      }

      const randomSnCode = () => {
        const rand1 = String(Math.floor(Math.random() * 10000)).padStart(4, '0');
        const date = new Date().toISOString().slice(0,10).replace(/-/g,'');
        const rand2 = String(Math.floor(Math.random() * 10000)).padStart(4, '0');
        return `${rand1}${date}${rand2}`;
      };

      const onClickTab = ({ title }) => {
        vant.showToast('已切换为'+title);
        if(title == '国内'){
          columns.value = <%= raw @products %>;
        } else if(title == '美亚'){
          columns.value = <%= raw @products_oversea %>;
        } else if(title == '英亚'){
          columns.value = <%= raw @products_oversea_uk %>;
        } else if(title == '欧亚'){
          columns.value = <%= raw @products_oversea_euro %>;
        } else if(title == '日亚'){
          columns.value = <%= raw @products_oversea_japan %>;
        }
        country_code.value = title
      }

      const connectDevice = async () => {
        try {
          const devices = await navigator.hid.requestDevice({
            filters: [
              {
                usagePage: 0xff60,
                usage: 0x61,
              },
              {
                usagePage: 0x0001,
                usage: 0x0000
              },
            ]
          });

          if (devices.length == 0) {
            console.log("No device selected")
            return;
          }

          const device = devices[0];

          if (!device.opened) {
            await device.open();
            deviceConnected.value = true;
            vant.showNotify({ type: 'success', message: `${device.productName} 已连接` });
            console.log(`Open device: \n${device.productName}\nPID-${device.productId} VID-${device.vendorId}\n\n`)
            device.addEventListener("inputreport", event => {
              let array = new Uint8Array(event.data.buffer);
              let hexstr = "";
              for (const data of array) {
                hexstr += (Array(2).join(0) + data.toString(16).toUpperCase()).slice(-2) + " ";
              }
              console.log('hexstr', hexstr);

              // 解析4B命令返回的SN码
              if (hexstr.startsWith('4B')) {
                const length = parseInt(hexstr.substr(3, 2), 16);
                const hexSnCode = hexstr.substr(6, length * 3 - 1).split(' ').join('');
                const decimalSn = [];
                for (let i = 0; i < hexSnCode.length; i += 2) {
                  decimalSn.push(parseInt(hexSnCode.substr(i, 2), 16));
                }
                device.sn = decimalSn.join('');
                console.log('Got device SN:', device.sn);
                if (device.sn === '5722757227572275722757227572275722757227') {
                  sn_code.value = '';
                } else {
                  sn_code.value = device.sn;
                }
              }
            });
            // 先发送4B命令读取SN码
            setTimeout(() => {
              send_data(device, '4B100000000000000000000000000000000010'.padEnd(64, '0'));
            }, 200);

            setTimeout(() => {
              fetch('/home/<USER>', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                  product_name: productName.value,
                  sku_name: skuName.value
                })
              })
              .then(response => response.json())
              .then((response) => {
                if(response.success == true){
                  const hexColor = response.hex_color;
                  const colorLength = (hexColor.length/2).toString(16).padStart(2, '0').toUpperCase();
                  let sum = parseInt(colorLength, 16);
                  for(let i = 0; i < hexColor.length; i+=2) {
                    sum += parseInt(hexColor.substr(i,2), 16);
                  }
                  const checksum = (sum & 0xFF).toString(16).padStart(2, '0').toUpperCase();
                  const data = `61${colorLength}${hexColor}${checksum}`.padEnd(64, '0');
                  console.log('color1-data', data);
                  send_data(device, data);
                }
              })
            }, 100);

            // 等待500ms读取返回值
            await new Promise(resolve => setTimeout(resolve, 500));

            // 如果没有获取到SN码，则生 成新的并写入
            if (!sn_code.value) {
              const sn = randomSnCode();
              console.log('sn', sn);
              // Convert sn to hex string
              const snHex = Array.from(sn).map(c => c.toString(16).padStart(2,'0')).join('').toUpperCase();
              const snLength = (snHex.length/2).toString(16).padStart(2, '0').toUpperCase();
              let sum = parseInt(snLength, 16);
              for(let i = 0; i < snHex.length; i+=2) {
                sum += parseInt(snHex.substr(i,2), 16);
              }
              const checksum = (sum & 0xFF).toString(16).padStart(2, '0').toUpperCase();
              const data = `4C${snLength}${snHex}${checksum}`.padEnd(64, '0');
              await send_data(device, data);

              // 发送4B命令再次读取SN码
              setTimeout(() => {
                send_data(device, '4B100000000000000000000000000000000010'.padEnd(64, '0'));
              }, 200);
            }
          } else {
            deviceConnected.value = true;
            vant.showNotify({ type: 'success', message: `${device.productName} 已连接` });
            console.log(`Open device: \n${device.productName}\nPID-${device.productId} VID-${device.vendorId}\n\n`)
            // 先发送4B命令读取SN码
            setTimeout(() => {
              send_data(device, '4B100000000000000000000000000000000010'.padEnd(64, '0'));
            }, 200);

            setTimeout(() => {
              fetch('/home/<USER>', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                  product_name: productName.value,
                  sku_name: skuName.value
                })
              })
              .then(response => response.json())
              .then((response) => {
                if(response.success == true){
                  const hexColor = response.hex_color;
                  const colorLength = (hexColor.length/2).toString(16).padStart(2, '0').toUpperCase();
                  let sum = parseInt(colorLength, 16);
                  for(let i = 0; i < hexColor.length; i+=2) {
                    sum += parseInt(hexColor.substr(i,2), 16);
                  }
                  const checksum = (sum & 0xFF).toString(16).padStart(2, '0').toUpperCase();
                  const data = `61${colorLength}${hexColor}${checksum}`.padEnd(64, '0');
                  console.log('color2-data', data);
                  send_data(device, data);
                }
              })
            }, 100);

            // 等待500ms读取返回值
            await new Promise(resolve => setTimeout(resolve, 500));

            // 如果没有获取到SN码，则生成新的并写入
            if (!sn_code.value) {
              const sn = randomSnCode();
              console.log('sn', sn);
              // Convert sn to hex string
              const snHex = Array.from(sn).map(c => c.toString(16).padStart(2,'0')).join('').toUpperCase();
              const snLength = (snHex.length/2).toString(16).padStart(2, '0').toUpperCase();
              let sum = parseInt(snLength, 16);
              for(let i = 0; i < snHex.length; i+=2) {
                sum += parseInt(snHex.substr(i,2), 16);
              }
              const checksum = (sum & 0xFF).toString(16).padStart(2, '0').toUpperCase();
              const data = `4C${snLength}${snHex}${checksum}`.padEnd(64, '0');
              await send_data(device, data);

              // 发送4B命令再次读取SN码
              setTimeout(() => {
                send_data(device, '4B100000000000000000000000000000000010'.padEnd(64, '0'));
              }, 200);
            }
          }

        } catch (error) {
          console.log(error);
          vant.showNotify({ type: 'danger', message: error.message });
        }
      }

      // 监听设备断开连接事件
      try {
        navigator.hid.addEventListener('disconnect', (event) => {
          if (event.device.collections[0].outputReports && event.device.collections[0].outputReports.length > 0) {
            deviceConnected.value = false;
            sn_code.value = '';
            vant.showNotify({ type: 'danger', message: `${event.device.productName} 已失去连接` });
          }
        });
      } catch (error) {
        console.log(error);
        // vant.showNotify({ type: 'danger', message: error.message });
      }

      const send_data = async (device,iptOutput) => {
        try {
          if (!device?.opened) {
            throw "Device not opened";
          }
          const outputData = new Uint8Array(32); // 要发送的数据包

          let outputDatastr = iptOutput.replace(/\s+/g, ""); // 去除所有空白字符
          // console.log('outputDatastr', outputDatastr);
          if (outputDatastr.length % 2 == 0 && /^[0-9a-fA-F]+$/.test(outputDatastr)) {
            // 检查长度和字符是否正确
            const byteLength = outputDatastr.length / 2 > 32 ? 32 : outputDatastr.length / 2;
            // 将字符串转成字节数组数据
            for (let i = 0; i < byteLength; i++) {
              outputData[i] = parseInt(outputDatastr.substr(i * 2, 2), 16);
            }
          } else {
            throw "Data is not even or 0-9、a-f、A-F";
          }
          // console.log('outputData', outputData);
          await device.sendReport(0, outputData); // 发送数据，第一个参数为reportId，填0表示不使用reportId
          // console.log(`Sent ${outputData.byteLength} bytes\n\n`);
        } catch (error) {
          console.log(error);
        }
      }

      // 下拉刷新
      const onRefresh = () => {
        window.location.reload();
      };

      return {
        columns,
        value,
        search_value,
        onSearch,
        print,
        selectedValues,
        is_loading,
        pull_loading,
        onRefresh,
        active,
        country_code,
        onClickTab,
        onChange,
        connectDevice,
        deviceConnected,
        isEZ80Product
      };
    },
  }
  const app = Vue.createApp(App);

  app.use(vant);
  app.use(vant.Lazyload);
  app.mount('#app');
</script>
