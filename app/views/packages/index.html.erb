<div class="container">
  <div class="row mb-4 mt-4">
    <div class="col">
      <h3>第一步：请先设置多少台一箱</h3>
      <div class="input-group">
        <input type="text" id="package_count" class="form-control" value="<%= params[:package_count] %>">
        <button id="save_package_count" class="btn btn-primary">设置</button>
      </div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col">
      <h3>第二步：请录入单品SN码</h3>
      <div id="progress-info" class="alert alert-info">
        已录入 <span id="current-count">0</span> / <span id="total-count"><%= params[:package_count] %></span>
      </div>
      <input type="text" id="sn_code" placeholder="请扫描SN码" class="form-control" autofocus="true" autocomplete="off">
      <div id="error-message" class="alert alert-danger mt-2 d-none"></div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col">
      <h6>已录入的SN码列表</h6>
      <table class="table" id="sn-list">
        <thead>
          <tr>
            <th>序号</th>
            <th>SN码</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>

  <div class="row">
    <div class="col">
      <button id="generate-super-sn" class="btn btn-success" disabled>生成并打印超级SN码</button>
      <p class="text-muted">后续出货绑定渠道商时无需拆箱，直接扫描整箱货的超级SN码即可</p>
    </div>
  </div>

  <!-- <div id="super-sn-result" class="mt-4 d-none">
    <h2>超级SN码</h2>
    <div class="card">
      <div class="card-body">
        <h3 id="super-sn-code"></h3>
        <div id="super-sn-qrcode"></div>
        <button class="btn btn-primary mt-2" id="copy-super-sn">打印超级SN码</button>
        <button class="btn btn-secondary mt-2" id="print-super-sn">打印标签</button>
      </div>
    </div>
  </div> -->
</div>

<script>
  if (typeof snCodes === 'undefined') {
    var snCodes = [];
  }
  var generateBtn = document.getElementById('generate-super-sn');
  var originalBtnText = generateBtn.textContent;
  // 从cookies中恢复数据
  function loadFromCookies() {
    var savedCodes = getCookie('snCodes');
    if (savedCodes) {
      snCodes = JSON.parse(savedCodes);
      updateSnList();
      updateProgress();
      checkAutoGenerate();
    }
  }

  // 保存数据到cookies
  function saveToCookies() {
    setCookie('snCodes', JSON.stringify(snCodes), 1); // 保存1天
  }

  // Cookie操作辅助函数
  function setCookie(name, value, days) {
    let expires = "";
    if (days) {
      const date = new Date();
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
      expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + "=" + (value || "") + expires + "; path=/";
  }

  function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for(let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) == ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  // 页面加载时恢复数据
  document.addEventListener('DOMContentLoaded', loadFromCookies);

  document.getElementById('save_package_count').addEventListener('click', function() {
    const packageCount = document.getElementById('package_count').value;
    window.location.href = "/packages?package_count=" + packageCount;
  });

  document.getElementById('sn_code').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      e.preventDefault();
      const snCode = e.target.value;
      const errorDiv = document.getElementById('error-message');

      // SN码格式验证
      if (!/^[A-Za-z0-9]{16}$/.test(snCode)) {
        errorDiv.textContent = 'SN码必须是16位数字';
        errorDiv.classList.remove('d-none');
        e.target.value = '';
        return;
      }

      if (snCodes.includes(snCode)) {
        errorDiv.textContent = '该SN码已存在';
        errorDiv.classList.remove('d-none');
        e.target.value = '';
        return;
      }

      if (snCode.length > 0) {
        addSnCode(snCode);
        e.target.value = '';
        errorDiv.classList.add('d-none');
      }
    }
  });

  function addSnCode(code) {
    snCodes.push(code);
    updateSnList();
    updateProgress();
    checkAutoGenerate();
    saveToCookies();
  }

  function updateSnList() {
    const tbody = document.querySelector('#sn-list tbody');
    tbody.innerHTML = '';
    snCodes.forEach((code, index) => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${index + 1}</td>
        <td>${code}</td>
        <td>
          <button class="btn btn-sm btn-danger" onclick="deleteSnCode(${index})">删除</button>
        </td>
      `;
      tbody.appendChild(tr);
    });
  }

  function deleteSnCode(index) {
    snCodes.splice(index, 1);
    updateSnList();
    updateProgress();
    checkAutoGenerate();
    saveToCookies();
  }

  function updateProgress() {
    const currentCount = document.getElementById('current-count');
    currentCount.textContent = snCodes.length;
    generateBtn.disabled = snCodes.length === 0;
  }

  function checkAutoGenerate() {
    const packageCount = parseInt(document.getElementById('package_count').value);
    if (snCodes.length === packageCount) {

    }
  }

  generateBtn.addEventListener('click', function() {
    generateBtn.textContent = '正在打印中...';
    generateBtn.disabled = true;

    fetch('/packages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({
        sn_codes: snCodes
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 清空当前录入的内容
        snCodes = [];
        updateSnList();
        updateProgress();
        saveToCookies();
        alert('打印成功');
        window.location.reload();
      } else {
        alert(data.error || '生成失败');
      }
      generateBtn.textContent = originalBtnText;
      generateBtn.disabled = snCodes.length === 0;
    })
    .catch(error => {
      console.error('Error:', error);
      alert('请求失败，请重试');
      generateBtn.textContent = originalBtnText;
      generateBtn.disabled = snCodes.length === 0;
    });
  });

</script>