wb = xlsx_package.workbook
wb.styles do |style|
  heading = style.add_style alignment: {horizontal: :center}, b: true, sz: 14, bg_color: "0066CC", fg_color: "FF"
  date_cell = style.add_style alignment: {horizontal: :center}, format_code: "yyyy-mm-dd HH:MM:ss", sz: 12
  data = style.add_style alignment: {horizontal: :center}, sz: 12
  wb.add_worksheet(name: "SN码列表") do |sheet|
    sheet.add_row %w(渠道商名称 SN码 商品名 SKU EAN码 UPC码 SN码创建时间 录入时间), style: heading
    @serial_numbers.each do |serial_number|
      sheet.add_row ["#{serial_number.channel_partner.name}", "#{serial_number.code}\t", "#{serial_number.sku.product.name}", "#{serial_number.sku.name}", "#{serial_number.sku.ean_code}\t", "#{serial_number.sku.upc_code}\t", "#{serial_number.created_at}", "#{serial_number.binded_at}"],
        style: [data, data, data, data, data, data, date_cell, date_cell]
    end
  end
end
