<% if serial_number.present? %>
  <% if serial_number.channel_partner.present? %>
    <table class="table">
      <thead>
        <tr>
          <th>渠道商名称</th>
          <th>SN码</th>
          <th>商品名</th>
          <th>SKU</th>
          <th>SN码创建时间</th>
          <th>录入时间</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td><%= serial_number.channel_partner.name %></td>
          <td><%= serial_number.code %></td>
          <td><%= serial_number.sku.product.name %></td>
          <td><%= serial_number.sku.name %></td>
          <td><%= serial_number.created_at.strftime("%Y-%m-%d %H:%M:%S") %></td>
          <td><%= serial_number.binded_at&.strftime("%Y-%m-%d %H:%M:%S") || "-" %></td>
        </tr>
      </tbody>
    </table>
  <% else %>
    <table class="table">
      <thead>
        <tr>
          <th>渠道商名称</th>
          <th>SN码</th>
          <th>商品名</th>
          <th>SKU</th>
          <th>SN码创建时间</th>
          <th>录入时间</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>-</td>
          <td><%= serial_number.code %></td>
          <td><%= serial_number.sku.product.name %></td>
          <td><%= serial_number.sku.name %></td>
          <td><%= serial_number.created_at.strftime("%Y-%m-%d %H:%M:%S") %></td>
          <td><%= serial_number.binded_at&.strftime("%Y-%m-%d %H:%M:%S") || "-" %></td>
        </tr>
      </tbody>
    </table>
  <% end %>
<% else %>
  <div class="alert alert-danger mt-3" role="alert">
    SN码不存在
  </div>
<% end %>