<%= form_with(model: channel_partner) do |form| %>
  <% if channel_partner.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(channel_partner.errors.count, "error") %> prohibited this channel_partner from being saved:</h2>

      <ul>
        <% channel_partner.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :name, "渠道商名称" , class: "form-label" %>
    <%= form.text_field :name, class: "form-control" %>
  </div>

  <div>
    <%= form.submit "保存", class: "btn btn-success mt-2" %>
  </div>
<% end %>
