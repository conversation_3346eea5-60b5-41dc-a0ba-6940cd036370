<div class="container">
  <% if flash[:success].present? %>
    <div class="alert alert-success mt-2" role="alert">
      <%= flash[:success] %>
    </div>
  <% elsif flash[:error].present? %>
    <div class="alert alert-danger mt-2" role="alert">
      <%= flash[:error] %>
    </div>
  <% end %>

  <h1>渠道商名称：<%= @channel_partner.name %><span class="text-success" style="font-size: 20px">（今日已录入 <%= @channel_partner.serial_numbers.where(binded_at: Time.current.beginning_of_day..Time.current.end_of_day).count %> 条）</span></h1>
  <%= link_to "重新选择渠道商", channel_partners_path %>
  <div>
    <%= form_with url: add_sn_code_channel_partner_path(@channel_partner), local: true do |form| %>
      <% if @channel_partner.errors.any? %>
        <div id='error_explanation'>
          <h2><%= pluralize(@channel_partner.errors.count, 'error') %> prohibited this from being saved:</h2>
            <ul>
              <% @channel_partner.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
        </div>
      <% end %>

      <div class='form-group mt-4'>
        <%= form.label :sn_code, "自动录入SN码", class: 'form-label' %>
        <span class="text-muted">（新功能: 扫描整箱货的超级SN码，会自动录入整箱货内的所有单品SN码，前提需要使用<a href="/packages?package_count=10" target="_blank">封箱工具</a>录入整箱货并生成超级SN码）</span>
        <%= form.text_field :sn_code, autofocus: true, autocomplete: 'off', class: 'form-control' %>
      </div>
    <% end %>
  </div>
  <div class="mt-3">
    <table class="table">
      <thead>
        <tr>
          <th>渠道商名称</th>
          <th>SN码</th>
          <th>商品名</th>
          <th>SKU</th>
          <th>SN码创建时间</th>
          <th>录入时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <% @final_serial_numbers.each do |serial_number| %>
          <tr>
            <td><%= serial_number.channel_partner.name %></td>
            <td><%= serial_number.code %></td>
            <td><%= serial_number.sku.product.name %></td>
            <td><%= serial_number.sku.name %></td>
            <td><%= serial_number.created_at.strftime("%Y-%m-%d %H:%M:%S") %></td>
            <td><%= serial_number.binded_at&.strftime("%Y-%m-%d %H:%M:%S") || "-" %></td>
            <td>
              <%= link_to "解除绑定", delete_sn_code_channel_partner_path(@channel_partner, sn_code: serial_number.code), data: { turbo_confirm: "确定要和此渠道商解除绑定吗？", turbo_method: :delete } %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%= paginate @final_serial_numbers %>
  </div>
</div>