<div class="container">
  <% if flash[:success].present? %>
    <div class="alert alert-success mt-2" role="alert">
      <%= flash[:success] %>
    </div>
  <% elsif flash[:error].present? %>
    <div class="alert alert-danger mt-2" role="alert">
      <%= flash[:error] %>
    </div>
  <% end %>

  <div class="d-flex mt-3" style="justify-content: space-between">
    <div><h3>溯源系统</h3></div>
    <div>
      <%= link_to "添加渠道商", new_channel_partner_path, class: "btn btn-secondary" %>
      <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#staticBackdrop">
        商品溯源
      </button>
    </div>
  </div>

  <div id="channel_partners">
    <div class="d-flex mb-3 mt-3">
      <%= form_with model: @q, local: true, url: channel_partners_path, method: :get do |form| %>
        <div class="row g-3 align-items-center">
          <div class="col-auto">
            <%= form.text_field :name_cont, value: params['q']&.[]('name_cont'), class: 'form-control', placeholder: '渠道商名称' %>
          </div>
          <div class="col-auto">
            <%= form.submit "搜索", class: 'btn btn-primary', style: "margin-right: 10px" %>
          </div>
        </div>
      <% end %>
    </div>

    <div>
      <table class="table">
        <thead>
          <tr>
            <th>渠道商名称</th>
            <th>操作</th>
          </tr>
        </thead>

        <tbody>
          <% @channel_partners.each do |channel_partner| %>
            <tr>
              <td><%= channel_partner.name %></td>
              <td>
                <%= link_to "录入SN码", channel_partner, style: "margin-right: 20px" %>
                <%= link_to "编辑", edit_channel_partner_path(channel_partner), style: "margin-right: 20px" %>
                <%= link_to "导出", channel_partner_path(channel_partner, format: :xlsx) %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
    <div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
      <div class="modal-dialog modal-xl">
        <div class="modal-content" data-controller="channel-partner">
          <div class="modal-header">
            <h1 class="modal-title fs-5" id="staticBackdropLabel">商品溯源</h1>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row g-3 align-items-center">
              <div class="col-auto">
                <%= label_tag :sn_code, "SN码", class: 'col-form-label' %>
              </div>
              <div class="col-auto">
                <%= text_field_tag :sn_code, "", class: 'form-control' %>
              </div>
              <div class="col-auto">
                <%= button_tag "搜索", class: 'btn btn-primary', id: "sn_code_search_btn", data: { action: "click->channel-partner#updateContent"} %>
              </div>
            </div>

            <div data-channel-partner-target="result" id="result">
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          </div>
        </div>
      </div>
    </div>
    <!-- 分页 -->
    <%= paginate @channel_partners %>
  </div>
</div>