class PackagesController < ApplicationController
  def index
  end

  def create
    sn_codes = params[:sn_codes]
    super_sn_code = random_sn_code
    if SuperSerialNumber.where(code: super_sn_code).exists?
      render json: { success: false, error: '超级SN码已存在，请重试' }
      return
    end

    if SerialNumber.where(code: super_sn_code).exists?
      render json: { success: false, error: 'SN码已存在，请重试' }
      return
    end
    super_serial_number = SuperSerialNumber.create(code: super_sn_code)
    @serial_numbers = SerialNumber.where(code: sn_codes)
    if @serial_numbers.update_all(super_serial_number_id: super_serial_number.id)
      @serial_numbers.first.sku.print(1, super_sn_code, '国内', 'KM118MW24160089')
      render json: { success: true }
    else
      render json: { success: false, error: '生成失败' }
    end
  end

  private
    def random_sn_code
      "#{format('%04d', (rand * 10000).to_i)}#{Time.current.strftime("%Y%m%d")}#{format('%04d', (rand * 10000).to_i)}"
    end

end
