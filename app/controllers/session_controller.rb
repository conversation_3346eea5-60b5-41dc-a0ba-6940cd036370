class SessionController < ApplicationController
  skip_before_action :verify_authenticity_token
  USER_PASSWORD = "user123"
  ADMIN_PASSWORD = "admin123"
  def new
    if params[:password] == USER_PASSWORD
      render json: { success: true, message: "登录成功", token: params[:password] }
    else
      render json: { success: false, message: "密码错误" }
    end
  end

  def check_password
    if params[:password] == USER_PASSWORD
      render json: { success: true }
    else
      render json: { success: false }
    end
  end

  def admin_login_new
  end

  def admin_login
    if params[:user][:password] == ADMIN_PASSWORD
      session[:password] = params[:user][:password]
      redirect_to products_path
    else
      redirect_to login_path, notice: "密码错误"
    end
  end

  def admin_logout
    session[:password] = nil
    redirect_to login_path
  end
end
