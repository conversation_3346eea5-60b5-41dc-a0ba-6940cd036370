class ChannelPartnersController < ApplicationController
  before_action :verify_password
  skip_forgery_protection only: :search
  before_action :set_channel_partner, only: %i[ show edit update destroy add_sn_code delete_sn_code ]

  # GET /channel_partners
  def index
    @q = ChannelPartner.ransack(params[:q])
    @channel_partners = @q.result(distinct: true).order("id desc").page(params[:page])
  end

  # GET /channel_partners/1
  def show
    @final_serial_numbers = @channel_partner.serial_numbers.order(binded_at: :desc).page(params[:page])
    respond_to do |format|
      format.html
      format.xlsx {
        @serial_numbers = @channel_partner.serial_numbers.order(binded_at: :desc)
        render xlsx: "index", filename: "#{@channel_partner.name}的SN码列表#{Time.now.strftime("%Y%m%d-%H%M%S")}.xlsx"
      }
    end
  end

  # GET /channel_partners/new
  def new
    @channel_partner = ChannelPartner.new
  end

  def add_sn_code
    sn_code = SerialNumber.find_by(code: params[:sn_code])
    if sn_code.nil?
      super_sn_code = SuperSerialNumber.find_by(code: params[:sn_code])
      if super_sn_code.nil?
        redirect_to @channel_partner, flash: { error: "录入失败，SN码 #{params[:sn_code]} 不存在！" } and return
      else
        super_sn_code.serial_numbers.each do |serial_number|
          serial_number.update!(channel_partner_id: @channel_partner.id, binded_at: Time.current)
        end
        redirect_to @channel_partner, flash: { success: "#{super_sn_code.code} 整箱货录入成功!" }
      end
    else
      if sn_code&.channel_partner.present?
        if sn_code.channel_partner.id == @channel_partner.id
          redirect_to @channel_partner, flash: { error: "重复录入，SN码 #{params[:sn_code]} 已经绑定此渠道商: #{sn_code.channel_partner.name}" }
        else
          redirect_to @channel_partner, flash: { error: "录入失败，SN码 #{params[:sn_code]} 已经绑定渠道商: #{sn_code.channel_partner.name}" }
        end
      else
        sn_code.update!(channel_partner_id: @channel_partner.id, binded_at: Time.current)
        redirect_to @channel_partner, flash: { success: "#{sn_code.code} 单个货品录入成功!" }
      end
    end
  end

  def delete_sn_code
    sn_code = SerialNumber.find_by(code: params[:sn_code])
    sn_code.update!(channel_partner_id: nil)
    redirect_to @channel_partner, flash: { success: "#{sn_code.code} 已解除绑定!" }
  end

  def search
    @serial_number = SerialNumber.find_by(code: params[:sn_code])
    render partial: "search_result", locals: { serial_number: @serial_number }
  end

  # GET /channel_partners/1/edit
  def edit
  end

  # POST /channel_partners
  def create
    @channel_partner = ChannelPartner.new(channel_partner_params)

    if @channel_partner.save
      redirect_to channel_partners_url, flash: { success: "渠道商创建成功！" }
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /channel_partners/1
  def update
    if @channel_partner.update(channel_partner_params)
      redirect_to channel_partners_url, flash: { success: "渠道商名称更新成功！" }
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /channel_partners/1
  def destroy
    @channel_partner.destroy
    redirect_to channel_partners_url, notice: "渠道商删除成功！", status: :see_other
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_channel_partner
      @channel_partner = ChannelPartner.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def channel_partner_params
      params.require(:channel_partner).permit(:name)
    end

    def verify_password
      if current_user.blank?
        redirect_to login_path
      end
    end
end
