# == Schema Information
#
# Table name: skus
#
#  id         :bigint           not null, primary key
#  name       :string(255)
#  upc_code   :string(255)
#  ean_code   :string(255)
#  china_code :string(255)
#  code       :string(255)
#  product_id :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  gtin_code  :string(255)
#  name_en    :string(255)
#  fn_code    :string(255)
#  fn_code_uk :string(255)
#
class Sku < ApplicationRecord
  has_many :serial_numbers, dependent: :restrict_with_error
  belongs_to :product
  validates :name, presence: true

  URL = 'https://cloud.kuaimai.com'
  def print(print_times, tmp_sn_code, country_code, device_code)

    Rails.logger.info("----------------打印机开始打印--------#{Time.current.strftime("%F %T")}--------")
    Rails.logger.info("print_times: #{print_times}, country_code: #{country_code}, device_code: #{device_code}, tmp_sn_code: #{tmp_sn_code}")
    if ['渠道', '国内'].include?(country_code) && self.product.need_sn? && tmp_sn_code.blank?
      return { status: false, message: '当前产品写入SN码失败，请重新连接设备' }
    end
    if country_code == '国内'
      current_time_str = Time.current.strftime("%F %T")
      tmp_ean_code = ean_code.present? ? "#{ean_code}" : nil

      render_data_array = []
      print_times.times do
        sn_code = tmp_sn_code.present? ? tmp_sn_code : random_sn_code
        if SerialNumber.find_by(code: sn_code).present?
          SerialNumber.find_by(code: sn_code).update(sku_id: id)
        else
          serial_numbers.create(code: sn_code)
        end

        render_data_array << {
          products: [{
            ean_code: tmp_ean_code,
            upc_code: upc_code,
            sku_code: code,
            sn_code: sn_code,
            mini_sn_code: sn_code[-4, 4],
            china_code: china_code,
            name: product&.name.to_s + '-' + name.to_s,
            name_en: product&.name_en.to_s + '-' + name_en.to_s,
            current_date: Time.current.strftime("%y/%m")
          }]
        }
      end
      zh_device_code = device_code.present? ? device_code : 'KM118MW23180146'
      if product.name.include?('Meet68')
        request_body = {
          appId: '1693289595946',
          timestamp: current_time_str,
          sn: zh_device_code,
          templateId: 1634980321,
          renderDataArray: render_data_array.to_json.to_s,
          printTimes: 1
        }
      else
        request_body = {
          appId: '1693289595946',
          timestamp: current_time_str,
          sn: zh_device_code,
          templateId: 1634980737,
          renderDataArray: render_data_array.to_json.to_s,
          printTimes: 1
        }
      end
      # 计算签名
      str = "2d105fd925bd4dc39499a22f5956f560appId#{request_body[:appId]}printTimes#{request_body[:printTimes]}renderDataArray#{request_body[:renderDataArray]}sn#{request_body[:sn]}templateId#{request_body[:templateId]}timestamp#{current_time_str}2d105fd925bd4dc39499a22f5956f560"
      p str
      sign = Digest::MD5.hexdigest(str)
      p sign
      request_body[:sign] = sign

      response = Faraday.post("#{URL}/api/cloud/print/tsplTemplatePrint", request_body.to_json, { 'Content-Type' => 'application/json' })
      JSON.parse(response.body)
    elsif country_code == '渠道'
      current_time_str = Time.current.strftime("%F %T")
      tmp_ean_code = ean_code.present? ? "#{ean_code}" : nil

      render_data_array = []
      print_times.times do
        sn_code = tmp_sn_code.present? ? tmp_sn_code : random_sn_code
        if SerialNumber.find_by(code: sn_code).present?
          SerialNumber.find_by(code: sn_code).update(sku_id: id)
        else
          serial_numbers.create(code: sn_code)
        end
        render_data_array << {
          products: [{
            ean_code: tmp_ean_code,
            upc_code: upc_code,
            sku_code: code,
            sn_code: sn_code,
            mini_sn_code: sn_code[-4, 4],
            china_code: china_code,
            name: product&.name.to_s + '-' + name.to_s,
            name_en: product&.name_en.to_s + '-' + name_en.to_s,
            current_date: Time.current.strftime("%y/%m")
          }]
        }
      end

      zh_device_code = device_code.present? ? device_code : 'KM118MW23180146'
      if product.name.include?('Meet68')
        request_body = {
          appId: '1693289595946',
          timestamp: current_time_str,
          sn: zh_device_code,
          templateId: 1634980321,
          renderDataArray: render_data_array.to_json.to_s,
          printTimes: 1
        }
      else
        request_body = {
          appId: '1693289595946',
          timestamp: current_time_str,
          sn: zh_device_code,
          templateId: 1634980737,
          renderDataArray: render_data_array.to_json.to_s,
          printTimes: 1
        }
      end
      # 计算签名
      str = "2d105fd925bd4dc39499a22f5956f560appId#{request_body[:appId]}printTimes#{request_body[:printTimes]}renderDataArray#{request_body[:renderDataArray]}sn#{request_body[:sn]}templateId#{request_body[:templateId]}timestamp#{current_time_str}2d105fd925bd4dc39499a22f5956f560"
      p str
      sign = Digest::MD5.hexdigest(str)
      p sign
      request_body[:sign] = sign

      response = Faraday.post("#{URL}/api/cloud/print/tsplTemplatePrint", request_body.to_json, { 'Content-Type' => 'application/json' })
      JSON.parse(response.body)
    elsif country_code == '美亚'
      current_time_str = Time.current.strftime("%F %T")
      request_body = {
        appId: '1693289595946',
        timestamp: current_time_str,
        sn: 'KM118MW23180384',
        templateId: 1634959538,
        renderDataArray: [
          {
            products: [{
              fn_code: fn_code,
              name: name_en,
            }]
          }
        ].to_json.to_s,
        printTimes: print_times
      }
      # 计算签名
      str = "2d105fd925bd4dc39499a22f5956f560appId#{request_body[:appId]}printTimes#{request_body[:printTimes]}renderDataArray#{request_body[:renderDataArray]}sn#{request_body[:sn]}templateId#{request_body[:templateId]}timestamp#{current_time_str}2d105fd925bd4dc39499a22f5956f560"
      p str
      sign = Digest::MD5.hexdigest(str)
      p sign
      request_body[:sign] = sign

      response = Faraday.post("#{URL}/api/cloud/print/tsplTemplatePrint", request_body.to_json, { 'Content-Type' => 'application/json' })
      JSON.parse(response.body)
    elsif country_code == '英亚'
      current_time_str = Time.current.strftime("%F %T")
      request_body = {
        appId: '1693289595946',
        timestamp: current_time_str,
        sn: 'KM118MW23180384',
        templateId: 1634959538,
        renderDataArray: [
          {
            products: [{
              fn_code: fn_code_uk,
              name: name_en,
            }]
          }
        ].to_json.to_s,
        printTimes: print_times
      }
      # 计算签名
      str = "2d105fd925bd4dc39499a22f5956f560appId#{request_body[:appId]}printTimes#{request_body[:printTimes]}renderDataArray#{request_body[:renderDataArray]}sn#{request_body[:sn]}templateId#{request_body[:templateId]}timestamp#{current_time_str}2d105fd925bd4dc39499a22f5956f560"
      p str
      sign = Digest::MD5.hexdigest(str)
      p sign
      request_body[:sign] = sign

      response = Faraday.post("#{URL}/api/cloud/print/tsplTemplatePrint", request_body.to_json, { 'Content-Type' => 'application/json' })
      JSON.parse(response.body)
    elsif country_code == '欧亚'
      current_time_str = Time.current.strftime("%F %T")
      request_body = {
        appId: '1693289595946',
        timestamp: current_time_str,
        sn: 'KM118MW23180384',
        templateId: 1634959538,
        renderDataArray: [
          {
            products: [{
              fn_code: fn_code_euro,
              name: name_en,
            }]
          }
        ].to_json.to_s,
        printTimes: print_times
      }
      # 计算签名
      str = "2d105fd925bd4dc39499a22f5956f560appId#{request_body[:appId]}printTimes#{request_body[:printTimes]}renderDataArray#{request_body[:renderDataArray]}sn#{request_body[:sn]}templateId#{request_body[:templateId]}timestamp#{current_time_str}2d105fd925bd4dc39499a22f5956f560"
      p str
      sign = Digest::MD5.hexdigest(str)
      p sign
      request_body[:sign] = sign

      response = Faraday.post("#{URL}/api/cloud/print/tsplTemplatePrint", request_body.to_json, { 'Content-Type' => 'application/json' })
      JSON.parse(response.body)
    elsif country_code == '日亚'
      current_time_str = Time.current.strftime("%F %T")
      request_body = {
        appId: '1693289595946',
        timestamp: current_time_str,
        sn: 'KM118MW23180384',
        templateId: 1634980719,
        renderDataArray: [
          {
            products: [{
              fn_code: fn_code_japan,
              name: name_en,
              sku_code: code,
            }]
          }
        ].to_json.to_s,
        printTimes: print_times
      }
      # 计算签名
      str = "2d105fd925bd4dc39499a22f5956f560appId#{request_body[:appId]}printTimes#{request_body[:printTimes]}renderDataArray#{request_body[:renderDataArray]}sn#{request_body[:sn]}templateId#{request_body[:templateId]}timestamp#{current_time_str}2d105fd925bd4dc39499a22f5956f560"
      p str
      sign = Digest::MD5.hexdigest(str)
      p sign
      request_body[:sign] = sign

      response = Faraday.post("#{URL}/api/cloud/print/tsplTemplatePrint", request_body.to_json, { 'Content-Type' => 'application/json' })
      JSON.parse(response.body)
    end
  end

  def random_sn_code
    "#{format('%04d', (rand * 10000).to_i)}#{Time.current.strftime("%Y%m%d")}#{format('%04d', (rand * 10000).to_i)}"
  end
end
