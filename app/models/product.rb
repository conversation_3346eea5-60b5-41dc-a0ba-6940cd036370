# == Schema Information
#
# Table name: products
#
#  id         :bigint           not null, primary key
#  name       :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  name_en    :string(255)
#  need_sn    :boolean
#
class Product < ApplicationRecord
  has_many :skus, dependent: :destroy
  accepts_nested_attributes_for :skus, allow_destroy: true
  validates :name, presence: true

  def skus_arr
    product_attr = {
      text: name,
      value: id
    }

    skus_attr = []

    skus.map do |sku|
      skus_attr << {
        text: sku.name,
        value: sku.id
      }
    end
    product_attr[:children] = skus_attr
    product_attr
  end

  def self.search(name = nil, country_code: '国内')
    products = if name
      if country_code == '国内'
        Product.where('name LIKE ?', "%#{name}%").order(:name)
      elsif country_code == '美亚'
        product_ids = Sku.where.not(fn_code: [nil, '']).pluck(:product_id).uniq
        Product.where(id: product_ids).where('name LIKE ?', "%#{name}%").order(:name)
      elsif country_code == '欧亚'
        product_ids = Sku.where.not(fn_code_euro: [nil, '']).pluck(:product_id).uniq
        Product.where(id: product_ids).where('name LIKE ?', "%#{name}%").order(:name)
      elsif country_code == '英亚'
        product_ids = Sku.where.not(fn_code_uk: [nil, '']).pluck(:product_id).uniq
        Product.where(id: product_ids).where('name LIKE ?', "%#{name}%").order(:name)
      elsif country_code == '日亚'
        product_ids = Sku.where.not(fn_code_japan: [nil, '']).pluck(:product_id).uniq
        Product.where(id: product_ids).where('name LIKE ?', "%#{name}%").order(:name)
      end
    else
      if country_code == '国内'
        Product.all.order(:name)
      elsif country_code == '美亚'
        product_ids = Sku.where.not(fn_code: [nil, '']).pluck(:product_id).uniq
        Product.where(id: product_ids).order(:name)
      elsif country_code == '欧亚'
        product_ids = Sku.where.not(fn_code_euro: [nil, '']).pluck(:product_id).uniq
        Product.where(id: product_ids).order(:name)
      elsif country_code == '英亚'
        product_ids = Sku.where.not(fn_code_uk: [nil, '']).pluck(:product_id).uniq
        Product.where(id: product_ids).order(:name)
      elsif country_code == '日亚'
        product_ids = Sku.where.not(fn_code_japan: [nil, '']).pluck(:product_id).uniq
        Product.where(id: product_ids).order(:name)
      end
    end

    products.map do |product|
      product.skus_arr
    end
  end

  def self.ransackable_attributes(auth_object = nil)
    ["created_at", "id", "name", "name_en", "need_sn", "updated_at"]
  end
end
