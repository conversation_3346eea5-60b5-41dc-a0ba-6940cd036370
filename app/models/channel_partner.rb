# == Schema Information
#
# Table name: channel_partners
#
#  id         :bigint           not null, primary key
#  name       :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class ChannelPartner < ApplicationRecord
  validates :name, presence: true, uniqueness: true
  has_many :serial_numbers

  def self.ransackable_attributes(auth_object = nil)
    ["created_at", "id", "name", "updated_at"]
  end
end
