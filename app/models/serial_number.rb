# == Schema Information
#
# Table name: serial_numbers
#
#  id                     :bigint           not null, primary key
#  code                   :string(255)
#  sku_id                 :integer
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  channel_partner_id     :integer
#  binded_at              :datetime
#  super_serial_number_id :integer
#
class SerialNumber < ApplicationRecord
  belongs_to :sku
  belongs_to :channel_partner, optional: true
  belongs_to :super_serial_number, optional: true
  validates :code, presence: true, uniqueness: true
end
