GEM
  remote: https://mirrors.gitee.com/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    airbrussh (1.5.2)
      sshkit (>= 1.6.1, != 1.7.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    base64 (0.2.0)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    builder (3.3.0)
    capistrano (3.19.1)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.1)
      capistrano (~> 3.1)
    capistrano-rails (1.6.3)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano3-puma (3.1.1)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (~> 3.4)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    caxlsx (4.1.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.4)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    concurrent-ruby (1.3.4)
    crass (1.0.6)
    date (3.3.4)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    erubi (1.13.0)
    faraday (2.10.1)
      faraday-net_http (>= 2.0, < 3.2)
      logger
    faraday-net_http (3.1.1)
      net-http
    globalid (1.2.1)
      activesupport (>= 6.1)
    htmlentities (4.3.4)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.0.1)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.7.2)
    irb (1.14.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    logger (1.6.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mini_mime (1.1.5)
    minitest (5.24.1)
    msgpack (1.7.2)
    mysql2 (0.5.6)
    net-http (0.4.1)
      uri
    net-imap (0.4.14)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.2.3)
    nio4r (2.7.3)
    nokogiri (1.16.7-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-linux)
      racc (~> 1.4)
    psych (5.1.2)
      stringio
    public_suffix (6.0.1)
    puma (3.12.6)
    racc (1.8.1)
    rack (2.2.9)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.2.1)
    ransack (4.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rdoc (6.7.0)
      psych (>= 4.0.0)
    regexp_parser (2.9.2)
    reline (0.5.9)
      io-console (~> 0.5)
    rexml (3.3.5)
      strscan
    rondo_form (0.2.6)
      railties (>= 6.0)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rubyzip (2.3.2)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.23.0)
      base64
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
    stimulus-rails (1.3.3)
      railties (>= 6.0.0)
    stringio (3.1.1)
    strscan (3.1.0)
    thor (1.3.1)
    timeout (0.4.1)
    turbo-rails (2.0.6)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uri (0.13.0)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    websocket (1.2.11)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.17)

PLATFORMS
  arm64-darwin-23
  arm64-darwin-24
  x86_64-linux

DEPENDENCIES
  annotate
  bootsnap
  capistrano-rails
  capistrano3-puma
  capybara
  caxlsx_rails
  debug
  faraday
  importmap-rails
  kaminari
  mysql2 (~> 0.5)
  puma (~> 3.7)
  rails (~> 7.0.6)
  ransack
  rondo_form
  roo
  selenium-webdriver
  sprockets-rails
  stimulus-rails
  turbo-rails
  tzinfo-data
  web-console
  webdrivers

RUBY VERSION
   ruby 3.1.3p185

BUNDLED WITH
   2.4.10
