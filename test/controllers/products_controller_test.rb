require "test_helper"

class ProductsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @product = products(:one)
  end

  test "should get index" do
    get products_url
    assert_response :success
  end

  test "should get new" do
    get new_product_url
    assert_response :success
  end

  test "should create product" do
    assert_difference("Product.count") do
      post products_url, params: { product: { name: @product.name, need_sn: true } }
    end

    assert_redirected_to products_url
  end

  test "should create product with need_sn false" do
    assert_difference("Product.count") do
      post products_url, params: { product: { name: "Test Product", need_sn: false } }
    end

    assert_redirected_to products_url
    assert_equal false, Product.last.need_sn
  end

  test "should show product" do
    get product_url(@product)
    assert_response :success
  end

  test "should get edit" do
    get edit_product_url(@product)
    assert_response :success
  end

  test "should update product" do
    patch product_url(@product), params: { product: { name: @product.name, need_sn: false } }
    assert_redirected_to products_url
  end

  test "should update product need_sn" do
    patch product_url(@product), params: { product: { name: @product.name, need_sn: false } }
    assert_redirected_to products_url
    @product.reload
    assert_equal false, @product.need_sn
  end

  test "should destroy product" do
    assert_difference("Product.count", -1) do
      delete product_url(@product)
    end

    assert_redirected_to products_url
  end
end
