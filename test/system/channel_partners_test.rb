require "application_system_test_case"

class ChannelPartnersTest < ApplicationSystemTestCase
  setup do
    @channel_partner = channel_partners(:one)
  end

  test "visiting the index" do
    visit channel_partners_url
    assert_selector "h1", text: "Channel partners"
  end

  test "should create channel partner" do
    visit channel_partners_url
    click_on "New channel partner"

    fill_in "Name", with: @channel_partner.name
    click_on "Create Channel partner"

    assert_text "Channel partner was successfully created"
    click_on "Back"
  end

  test "should update Channel partner" do
    visit channel_partner_url(@channel_partner)
    click_on "Edit this channel partner", match: :first

    fill_in "Name", with: @channel_partner.name
    click_on "Update Channel partner"

    assert_text "Channel partner was successfully updated"
    click_on "Back"
  end

  test "should destroy Channel partner" do
    visit channel_partner_url(@channel_partner)
    click_on "Destroy this channel partner", match: :first

    assert_text "Channel partner was successfully destroyed"
  end
end
