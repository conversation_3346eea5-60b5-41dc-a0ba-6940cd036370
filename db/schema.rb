# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_08_01_020953) do
  create_table "channel_partners", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "channel_type"
    t.integer "parent_channel_partner_id"
    t.integer "status"
  end

  create_table "entry_processes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "serial_number_id"
    t.integer "channel_partner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "entry_at"
    t.integer "operation_type"
    t.string "location"
    t.string "ip"
    t.index ["channel_partner_id"], name: "index_entry_processes_on_channel_partner_id"
    t.index ["serial_number_id"], name: "index_entry_processes_on_serial_number_id"
  end

  create_table "products", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name_en"
    t.boolean "need_sn"
  end

  create_table "serial_numbers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "code"
    t.integer "sku_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "channel_partner_id"
    t.datetime "binded_at"
    t.integer "super_serial_number_id"
    t.index ["channel_partner_id"], name: "index_serial_numbers_on_channel_partner_id"
    t.index ["code"], name: "index_serial_numbers_on_code", unique: true
    t.index ["sku_id"], name: "index_serial_numbers_on_sku_id"
    t.index ["super_serial_number_id"], name: "index_serial_numbers_on_super_serial_number_id"
  end

  create_table "skus", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "upc_code"
    t.string "ean_code"
    t.string "china_code"
    t.string "code"
    t.integer "product_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "gtin_code"
    t.string "name_en"
    t.string "fn_code"
    t.string "fn_code_uk"
    t.string "fn_code_euro"
    t.string "fn_code_japan"
    t.string "r_color"
    t.string "g_color"
    t.string "b_color"
    t.index ["product_id"], name: "index_skus_on_product_id"
  end

  create_table "super_serial_numbers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "username"
    t.string "phone"
    t.string "password_hash"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "channel_partner_id"
    t.index ["channel_partner_id"], name: "index_users_on_channel_partner_id"
    t.index ["phone"], name: "index_users_on_phone", unique: true
    t.index ["username"], name: "index_users_on_username", unique: true
  end

  create_table "verification_codes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "phone"
    t.string "code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "versions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "whodunnit"
    t.datetime "created_at"
    t.bigint "item_id", null: false
    t.string "item_type", limit: 191, null: false
    t.string "event", null: false
    t.text "object", size: :long
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

end
