require 'csv'

desc 'export products info'
task export_products_info: :environment do
  file_name = "#{Rails.root}/lib/tasks/商品信息.csv"
  field_name = %w(货品名称 货品英文名 规格 SKU英文名称 条码 UPC EAN 69码)

  csv_string = CSV.generate do |csv|
    csv << field_name
    Product.all.each do |product|
      product.skus.each do |sku|
        csv << [product.name, product.name_en, sku.name, sku.name_en, sku.code, sku.upc_code, sku.ean_code, sku.china_code]
      end
    end
  end
  File.write(file_name, csv_string)
end