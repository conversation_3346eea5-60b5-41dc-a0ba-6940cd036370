desc '导入商品'
task import_product: :environment do
  # roo_excel = Roo::Excelx.new(Rails.root.to_s + '/分仓库存查询.xlsx')
  # roo_excel.sheets.each do |sheet_name|
  #   spreadsheet = roo_excel.sheet(sheet_name)
  #   express_excel = spreadsheet.parse(headers: true)
  #   express_excel.delete_at(0)
  #   express_excel.each do |row|
  #     product = Product.find_or_create_by(name: row["货品名称"])
  #     product.skus.find_or_create_by(name: row["规格"]).update!(code: row["条码"])
  #   end
  # end

  # roo_excel = Roo::Excelx.new(Rails.root.to_s + '/条码表1.xlsx')
  # roo_excel.sheets.each do |sheet_name|
  #   spreadsheet = roo_excel.sheet(sheet_name)
  #   express_excel = spreadsheet.parse(headers: true)
  #   express_excel.delete_at(0)
  #   express_excel.each do |row|
  #     if row["SKU码"].present?
  #       sku = Sku.find_by(code: row["SKU码"].strip)
  #       upc_code = row["UPC 码"].present? ? row["UPC 码"].to_i.to_s&.strip : nil
  #       ean_code = row["EAN码"].present? ? row["EAN码"].to_i.to_s&.strip : nil
  #       china_code = row["69码"].present? ? row["69码"].to_i.to_s&.strip : nil
  #       sku&.update!(upc_code: upc_code, ean_code: ean_code, china_code: china_code)
  #     end
  #   end
  # end

  roo_excel = Roo::Excelx.new(Rails.root.to_s + '/GTIN码.xlsx')
  roo_excel.sheets.each do |sheet_name|
    spreadsheet = roo_excel.sheet(sheet_name)
    express_excel = spreadsheet.parse(headers: true)
    express_excel.delete_at(0)
    express_excel.each do |row|
      upc_code = row["UPC (US & Canada)"].present? ? row["UPC (US & Canada)"].to_i.to_s&.strip : nil
      if upc_code.present?
        p upc_code
        sku = Sku.find_by(upc_code: upc_code)
        gtin_code = row["GTIN-14 (Shipping Container Code)"].present? ? row["GTIN-14 (Shipping Container Code)"].to_i.to_s&.strip : nil
        sku&.update!(gtin_code: gtin_code)
      end
    end
  end
end
