Rails.application.routes.draw do
  resources :packages, only: [:index, :create]
  resources :channel_partners, only: [:index, :show, :new, :create, :edit, :update] do
    member do
      post :add_sn_code
      delete :delete_sn_code
    end
    collection do
      post :search_sn_code
      get :search
    end
  end
  post 'session/new'
  post 'session/check_password'
  get 'home/index'
  get 'home/search'
  get 'home/check_need_sn'
  post 'home/print'
  post 'home/get_rgb'
  get 'home/print_by_sn'
  resources :products do
    collection do
      post :import
      get :download_import_template
    end
  end
  root to: "home#index"
  post 'session/admin_logout'
  get :login, to: 'session#admin_login_new'
  post :login, to: 'session#admin_login'
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Defines the root path route ("/")
  # root "articles#index"
end
